use crate::types::EnrichedEntry;
use std::collections::HashSet;
use std::sync::Mutex;
use xxhash_rust::xxh3::xxh3_64;

pub struct EntryDeduplicator {
    inner: Mutex<DeduplicatorInner>,
}

struct DeduplicatorInner {
    seen_hashes: HashSet<u64>,
    current_slot: u64,
}

impl EntryDeduplicator {
    pub fn new() -> Self {
        Self { inner: Mutex::new(DeduplicatorInner { seen_hashes: HashSet::new(), current_slot: 0 }) }
    }

    pub fn should_process(&self, enriched_entry: &EnrichedEntry) -> bool {
        let mut inner = self.inner.lock().unwrap();

        if enriched_entry.entry.slot < inner.current_slot {
            return false;
        }

        if enriched_entry.entry.slot > inner.current_slot {
            inner.current_slot = enriched_entry.entry.slot;
            inner.seen_hashes.clear();
        }

        let hash = xxh3_64(&enriched_entry.entry.entries);
        inner.seen_hashes.insert(hash)
    }
}
