use serde::Deserialize;
use serde::Serialize;
use validator::Validate;

fn default_async_logging() -> bool {
    true
}

fn default_target_filtering() -> bool {
    true
}

fn default_compact_format() -> bool {
    true
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct LoggerConfig {
    #[validate(custom(function = "validate_log_level"))]
    pub level: String,

    pub format: LogFormat,

    #[serde(default = "default_async_logging")]
    pub async_logging: bool,

    #[serde(default = "default_target_filtering")]
    pub target_filtering: bool,

    #[serde(default)]
    pub module_filters: Option<String>,

    #[serde(default)]
    pub show_target: bool,

    #[serde(default)]
    pub show_thread_ids: bool,

    #[serde(default = "default_compact_format")]
    pub compact_format: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
#[serde(rename_all = "lowercase")]
pub enum LogFormat {
    J<PERSON>,
    #[default]
    Pretty,
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self { level: "info".to_string(), format: LogFormat::Pretty, async_logging: true, target_filtering: true, module_filters: None, show_target: false, show_thread_ids: false, compact_format: true }
    }
}

fn validate_log_level(level: &str) -> Result<(), validator::ValidationError> {
    match level.to_lowercase().as_str() {
        "trace" | "debug" | "info" | "warn" | "error" | "off" => Ok(()),
        _ => {
            let mut error = validator::ValidationError::new("invalid_log_level");
            error.message = Some("Log level must be one of: trace, debug, info, warn, error, off".into());
            Err(error)
        }
    }
}
