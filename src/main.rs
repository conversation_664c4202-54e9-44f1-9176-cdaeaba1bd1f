mod common;
mod config;
mod core;
mod generated;
mod types;
mod utils;

use common::endpoints::manager::EndpointManager;
use core::config::load_config;
use core::logger::Logger;
use std::time::{SystemTime, UNIX_EPOCH};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    rustls::crypto::ring::default_provider().install_default().expect("Failed to install crypto provider");

    let config = load_config()?;
    let _logger = Logger::init(&config.logger)?;

    let endpoint_count = config.endpoints.len();
    let mut endpoint_manager = EndpointManager::new(config.endpoints, config.filters);

    println!("Starting EndpointManager with {} endpoints...", endpoint_count);
    let mut receiver = endpoint_manager.subscribe().await?;
    println!("EndpointManager started! Listening for entries from all endpoints...");

    let mut entry_count = 0;

    while let Some(enriched_entry) = receiver.recv().await {
        let now_ns = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos() as u64;
        let processing_time_ns = now_ns - enriched_entry.received_at;
        let processing_time_us = processing_time_ns as f64 / 1000.0;

        entry_count += 1;

        println!(
            "Entry #{}: slot={}, data_size={} bytes, processing_time={:.2} μs, received_by={}",
            entry_count,
            enriched_entry.entry.slot,
            enriched_entry.entry.entries.len(),
            processing_time_us,
            enriched_entry.received_by
        );

        if entry_count >= 200 {
            println!("Received {} entries successfully. Test completed!", entry_count);
            break;
        }
    }

    Ok(())
}
